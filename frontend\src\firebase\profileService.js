import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  onSnapshot,
  serverTimestamp
} from 'firebase/firestore';
import { db } from './config';

// خدمة إدارة الملفات الشخصية في Firebase

/**
 * إنشاء أو تحديث الملف الشخصي
 * @param {string} userId - معرف المستخدم
 * @param {Object} profileData - بيانات الملف الشخصي
 * @returns {Promise<boolean>} true إذا تم الحفظ بنجاح
 */
export const saveUserProfile = async (userId, profileData) => {
  try {
    if (!userId) {
      throw new Error('معرف المستخدم مطلوب');
    }

    console.log('💾 حفظ الملف الشخصي في Firebase...', { userId, profileData });

    const profileRef = doc(db, 'profiles', userId);
    
    const profileToSave = {
      userId,
      name: profileData.name || 'مستخدم جديد',
      email: profileData.email || '',
      phone: profileData.phone || '',
      bio: profileData.bio || '',
      studentCode: profileData.studentCode || '',
      role: profileData.role || 'student',
      avatar: profileData.avatar || '',
      preferences: profileData.preferences || {
        language: 'ar',
        notifications: true,
        theme: 'light'
      },
      lastUpdated: serverTimestamp(),
      createdAt: profileData.createdAt || serverTimestamp()
    };

    // استخدام setDoc مع merge للحفاظ على البيانات الموجودة
    await setDoc(profileRef, profileToSave, { merge: true });
    
    console.log('✅ تم حفظ الملف الشخصي بنجاح في Firebase');
    return true;
  } catch (error) {
    console.error('❌ خطأ في حفظ الملف الشخصي:', error);
    throw error;
  }
};

/**
 * جلب الملف الشخصي
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Object|null>} بيانات الملف الشخصي أو null
 */
export const getUserProfile = async (userId) => {
  try {
    if (!userId) {
      throw new Error('معرف المستخدم مطلوب');
    }

    console.log('📥 جلب الملف الشخصي من Firebase...', userId);

    const profileRef = doc(db, 'profiles', userId);
    const profileSnap = await getDoc(profileRef);
    
    if (profileSnap.exists()) {
      const profileData = profileSnap.data();
      console.log('✅ تم جلب الملف الشخصي بنجاح:', profileData);
      return {
        id: profileSnap.id,
        ...profileData
      };
    } else {
      console.log('⚠️ الملف الشخصي غير موجود');
      return null;
    }
  } catch (error) {
    console.error('❌ خطأ في جلب الملف الشخصي:', error);
    throw error;
  }
};

/**
 * تحديث جزء من الملف الشخصي
 * @param {string} userId - معرف المستخدم
 * @param {Object} updates - التحديثات المطلوبة
 * @returns {Promise<boolean>} true إذا تم التحديث بنجاح
 */
export const updateUserProfile = async (userId, updates) => {
  try {
    if (!userId) {
      throw new Error('معرف المستخدم مطلوب');
    }

    console.log('🔄 تحديث الملف الشخصي في Firebase...', { userId, updates });

    const profileRef = doc(db, 'profiles', userId);
    
    const updateData = {
      ...updates,
      lastUpdated: serverTimestamp()
    };

    await updateDoc(profileRef, updateData);
    
    console.log('✅ تم تحديث الملف الشخصي بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تحديث الملف الشخصي:', error);
    throw error;
  }
};

/**
 * الاستماع للتغييرات في الملف الشخصي (Real-time)
 * @param {string} userId - معرف المستخدم
 * @param {Function} callback - دالة يتم استدعاؤها عند التغيير
 * @returns {Function} دالة إلغاء الاستماع
 */
export const subscribeToUserProfile = (userId, callback) => {
  try {
    if (!userId) {
      throw new Error('معرف المستخدم مطلوب');
    }

    console.log('👂 بدء الاستماع للتغييرات في الملف الشخصي...', userId);

    const profileRef = doc(db, 'profiles', userId);
    
    const unsubscribe = onSnapshot(profileRef, (doc) => {
      if (doc.exists()) {
        const profileData = {
          id: doc.id,
          ...doc.data()
        };
        console.log('🔄 تم تحديث الملف الشخصي:', profileData);
        callback(profileData);
      } else {
        console.log('⚠️ الملف الشخصي غير موجود');
        callback(null);
      }
    }, (error) => {
      console.error('❌ خطأ في الاستماع للملف الشخصي:', error);
      callback(null);
    });

    return unsubscribe;
  } catch (error) {
    console.error('❌ خطأ في إعداد الاستماع للملف الشخصي:', error);
    return () => {}; // دالة فارغة
  }
};

/**
 * التحقق من وجود الملف الشخصي وإنشاؤه إذا لم يكن موجوداً
 * @param {string} userId - معرف المستخدم
 * @param {Object} defaultData - البيانات الافتراضية
 * @returns {Promise<Object>} بيانات الملف الشخصي
 */
export const ensureUserProfile = async (userId, defaultData = {}) => {
  try {
    console.log('🔍 التحقق من وجود الملف الشخصي...', userId);

    // محاولة جلب الملف الشخصي
    let profile = await getUserProfile(userId);
    
    if (!profile) {
      console.log('📝 إنشاء ملف شخصي جديد...');
      
      // إنشاء ملف شخصي جديد
      const newProfileData = {
        name: defaultData.name || 'مستخدم جديد',
        email: defaultData.email || '',
        phone: defaultData.phone || '',
        bio: defaultData.bio || 'مرحباً بي في منصة SKILLS WORLD ACADEMY',
        studentCode: defaultData.studentCode || '',
        role: defaultData.role || 'student',
        avatar: defaultData.avatar || '',
        preferences: {
          language: 'ar',
          notifications: true,
          theme: 'light'
        }
      };

      await saveUserProfile(userId, newProfileData);
      profile = await getUserProfile(userId);
    }

    return profile;
  } catch (error) {
    console.error('❌ خطأ في التحقق من الملف الشخصي:', error);
    throw error;
  }
};

/**
 * حذف الملف الشخصي
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<boolean>} true إذا تم الحذف بنجاح
 */
export const deleteUserProfile = async (userId) => {
  try {
    if (!userId) {
      throw new Error('معرف المستخدم مطلوب');
    }

    console.log('🗑️ حذف الملف الشخصي...', userId);

    const profileRef = doc(db, 'profiles', userId);
    await deleteDoc(profileRef);
    
    console.log('✅ تم حذف الملف الشخصي بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في حذف الملف الشخصي:', error);
    throw error;
  }
};

/**
 * البحث في الملفات الشخصية
 * @param {string} searchTerm - مصطلح البحث
 * @returns {Promise<Array>} قائمة الملفات الشخصية المطابقة
 */
export const searchUserProfiles = async (searchTerm) => {
  try {
    console.log('🔍 البحث في الملفات الشخصية...', searchTerm);
    
    // يمكن تطوير هذه الدالة لاحقاً لتشمل البحث المتقدم
    // حالياً ترجع قائمة فارغة
    return [];
  } catch (error) {
    console.error('❌ خطأ في البحث:', error);
    throw error;
  }
};

/**
 * تحديث تفضيلات المستخدم
 * @param {string} userId - معرف المستخدم
 * @param {Object} preferences - التفضيلات الجديدة
 * @returns {Promise<boolean>} true إذا تم التحديث بنجاح
 */
export const updateUserPreferences = async (userId, preferences) => {
  try {
    console.log('⚙️ تحديث تفضيلات المستخدم...', { userId, preferences });

    return await updateUserProfile(userId, { preferences });
  } catch (error) {
    console.error('❌ خطأ في تحديث التفضيلات:', error);
    throw error;
  }
};

// تصدير جميع الدوال
export default {
  saveUserProfile,
  getUserProfile,
  updateUserProfile,
  subscribeToUserProfile,
  ensureUserProfile,
  deleteUserProfile,
  searchUserProfiles,
  updateUserPreferences
};
