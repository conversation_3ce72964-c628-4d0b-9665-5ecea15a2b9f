import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Tabs,
  Tab,
  Container,
  InputAdornment,
  IconButton,
  Fade,
  Slide,
  CircularProgress
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AdminPanelSettings,
  School,
  Login as LoginIcon,
  AutoAwesome,
  Psychology,
  Language,
  Phone,
  Email,
  MenuBook,
  ImportContacts,
  TrendingUp,
  BarChart,
  PieChart,
  ShowChart,
  Analytics,
  Campaign,
  Insights,
  Groups,
  Public,
  Share,
  ThumbUp,
  Star,
  Lightbulb,
  Rocket,
  CenterFocusStrong
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

const Login = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [language, setLanguage] = useState('ar'); // 'ar' for Arabic, 'en' for English
  
  // Admin login form
  const [adminForm, setAdminForm] = useState({
    email: '',
    password: ''
  });
  
  // Student login form
  const [studentForm, setStudentForm] = useState({
    code: ''
  });

  const { loginAdmin, loginStudent } = useAuth();

  // Language translations
  const translations = {
    ar: {
      adminLogin: 'تسجيل دخول المدير',
      studentLogin: 'تسجيل دخول الطالب',
      email: 'البريد الإلكتروني',
      password: 'كلمة المرور',
      studentCode: 'كود الطالب (6 أرقام)',
      login: 'دخول',
      loggingIn: 'جاري تسجيل الدخول...',
      studentCodeDesc: 'أدخل الكود المكون من 6 أرقام الذي حصلت عليه من المدير',
      platformDesc: '🌟 منصة التعلم والتطوير المهني 🌟',
      admin: 'المدير',
      student: 'الطالب'
    },
    en: {
      adminLogin: 'Admin Login',
      studentLogin: 'Student Login',
      email: 'Email Address',
      password: 'Password',
      studentCode: 'Student Code (6 digits)',
      login: 'Login',
      loggingIn: 'Logging in...',
      studentCodeDesc: 'Enter the 6-digit code you received from the admin',
      platformDesc: '🌟 Learning & Professional Development Platform 🌟',
      admin: 'Admin',
      student: 'Student'
    }
  };

  const t = translations[language];

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'ar' ? 'en' : 'ar');
  };

  const handleAdminSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    await loginAdmin(adminForm.email, adminForm.password);
    
    setLoading(false);
  };

  const handleStudentSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    await loginStudent(studentForm.code);
    
    setLoading(false);
  };

  const handleAdminChange = (field) => (e) => {
    setAdminForm(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleStudentChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setStudentForm({ code: value });
  };

  return (
    <Box
      dir={language === 'ar' ? 'rtl' : 'ltr'}
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 30%, #ffffff 60%, #3b82f6 90%, #1e3a8a 100%)',
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 80%, rgba(30, 58, 138, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
          pointerEvents: 'none'
        }
      }}
    >

      {/* Header */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          background: 'linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(59, 130, 246, 0.98) 100%)',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(255,255,255,0.3)',
          boxShadow: '0 2px 20px rgba(30, 58, 138, 0.3)'
        }}
      >
        <Container maxWidth="lg">
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              py: { xs: 1, sm: 1.5 },
              px: { xs: 1, sm: 2 }
            }}
          >
            {/* Logo and Brand */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <MenuBook sx={{
                fontSize: { xs: '1.5rem', sm: '2rem' },
                color: '#ffffff',
                filter: 'drop-shadow(0 0 6px rgba(255, 255, 255, 0.6))',
                transform: 'rotate(-5deg)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'rotate(0deg) scale(1.1)',
                  filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.8))'
                }
              }} />
              <Box>
                <Typography
                  sx={{
                    fontSize: { xs: '1rem', sm: '1.2rem', md: '1.4rem' },
                    fontWeight: 700,
                    color: '#ffffff',
                    fontFamily: '"Roboto", "Arial", sans-serif',
                    letterSpacing: '1px',
                    lineHeight: 1,
                    whiteSpace: 'nowrap',
                    textShadow: '0 2px 6px rgba(0,0,0,0.5)'
                  }}
                >
                  SKILLS WORLD ACADEMY
                </Typography>
                <Typography
                  sx={{
                    fontSize: { xs: '0.8rem', sm: '0.9rem' },
                    color: 'rgba(255,255,255,0.95)',
                    fontWeight: 500,
                    letterSpacing: '0.5px',
                    mt: 0.2,
                    textShadow: '0 2px 4px rgba(0,0,0,0.4)'
                  }}
                >
                  ALAA ABD HAMIED
                </Typography>
              </Box>
            </Box>

            {/* Contact Info */}
            <Box sx={{
              display: { xs: 'none', md: 'flex' },
              alignItems: 'center',
              gap: 3
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Phone sx={{ fontSize: '1rem', color: '#ffffff' }} />
                <Typography sx={{
                  fontSize: '0.9rem',
                  color: 'rgba(255,255,255,0.95)',
                  fontFamily: '"Roboto", sans-serif',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                }}>
                  0506747770
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Email sx={{ fontSize: '1rem', color: '#ffffff' }} />
                <Typography sx={{
                  fontSize: '0.9rem',
                  color: 'rgba(255,255,255,0.95)',
                  fontFamily: '"Roboto", sans-serif',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                }}>
                  <EMAIL>
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  cursor: 'pointer',
                  padding: '4px 8px',
                  borderRadius: '6px',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'rgba(255,255,255,0.2)',
                    transform: 'translateY(-1px)'
                  }
                }}
                onClick={toggleLanguage}
              >
                <Language sx={{ fontSize: '1rem', color: '#ffffff' }} />
                <Typography sx={{
                  fontSize: '0.9rem',
                  color: 'rgba(255,255,255,0.95)',
                  fontFamily: '"Roboto", sans-serif',
                  userSelect: 'none',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                }}>
                  {language === 'ar' ? 'العربية | EN' : 'Arabic | عربي'}
                </Typography>
              </Box>
            </Box>

            {/* Mobile Contact & Language */}
            <Box sx={{
              display: { xs: 'flex', md: 'none' },
              alignItems: 'center',
              gap: { xs: 0.5, sm: 1 }
            }}>
              {/* Language Toggle for Mobile */}
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.3,
                  cursor: 'pointer',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'rgba(255,255,255,0.2)'
                  }
                }}
                onClick={toggleLanguage}
              >
                <Language sx={{ fontSize: '1rem', color: '#ffffff' }} />
                <Typography sx={{
                  fontSize: '0.75rem',
                  color: 'rgba(255,255,255,0.95)',
                  fontFamily: '"Roboto", sans-serif',
                  userSelect: 'none',
                  fontWeight: 500,
                  textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                }}>
                  {language === 'ar' ? 'EN' : 'عر'}
                </Typography>
              </Box>

              {/* Contact Icons */}
              <Phone sx={{ fontSize: { xs: '1rem', sm: '1.2rem' }, color: '#ffffff' }} />
              <Email sx={{ fontSize: { xs: '1rem', sm: '1.2rem' }, color: '#ffffff' }} />
            </Box>
          </Box>
        </Container>
      </Box>

      {/* Main Content with top padding for fixed header */}
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: { xs: 1, sm: 2, md: 3, lg: 4 },
          paddingTop: { xs: '70px', sm: '80px', md: '90px', lg: '100px' },
          paddingBottom: { xs: 2, sm: 3, md: 4 },
          position: 'relative'
        }}
      >
        {/* Marketing Graphics Behind Login Card */}
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: { xs: '120%', sm: '140%', md: '160%', lg: '180%' },
            height: { xs: '120%', sm: '140%', md: '160%', lg: '180%' },
            pointerEvents: 'none',
            zIndex: 0,
            opacity: 0.08,
            overflow: 'hidden'
          }}
        >
          {/* Left Side Marketing Icons */}
          <TrendingUp sx={{
            position: 'absolute',
            top: '15%',
            left: '5%',
            fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
            color: '#2196F3',
            transform: 'rotate(-15deg)',
            animation: 'marketingFloat 8s ease-in-out infinite'
          }} />

          <BarChart sx={{
            position: 'absolute',
            top: '35%',
            left: '8%',
            fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
            color: '#4CAF50',
            transform: 'rotate(20deg)',
            animation: 'marketingFloat 10s ease-in-out infinite reverse'
          }} />

          <Campaign sx={{
            position: 'absolute',
            top: '60%',
            left: '10%',
            fontSize: { xs: '3.5rem', sm: '4.5rem', md: '5.5rem' },
            color: '#FF9800',
            transform: 'rotate(-25deg)',
            animation: 'marketingFloat 12s ease-in-out infinite'
          }} />

          <Groups sx={{
            position: 'absolute',
            bottom: '15%',
            left: '12%',
            fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
            color: '#9C27B0',
            transform: 'rotate(30deg)',
            animation: 'marketingFloat 9s ease-in-out infinite reverse'
          }} />

          {/* Right Side Marketing Icons */}
          <Analytics sx={{
            position: 'absolute',
            top: '20%',
            right: '8%',
            fontSize: { xs: '3.5rem', sm: '4.5rem', md: '5.5rem' },
            color: '#E91E63',
            transform: 'rotate(25deg)',
            animation: 'marketingFloat 11s ease-in-out infinite'
          }} />

          <PieChart sx={{
            position: 'absolute',
            top: '45%',
            right: '5%',
            fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
            color: '#00BCD4',
            transform: 'rotate(-20deg)',
            animation: 'marketingFloat 7s ease-in-out infinite reverse'
          }} />

          <Insights sx={{
            position: 'absolute',
            bottom: '25%',
            right: '10%',
            fontSize: { xs: '2.8rem', sm: '3.8rem', md: '4.8rem' },
            color: '#607D8B',
            transform: 'rotate(35deg)',
            animation: 'marketingFloat 13s ease-in-out infinite'
          }} />

          <CenterFocusStrong sx={{
            position: 'absolute',
            bottom: '10%',
            right: '15%',
            fontSize: { xs: '3.2rem', sm: '4.2rem', md: '5.2rem' },
            color: '#F44336',
            transform: 'rotate(-30deg)',
            animation: 'marketingFloat 6s ease-in-out infinite reverse'
          }} />

          {/* Top and Bottom Decorative Elements */}
          <ShowChart sx={{
            position: 'absolute',
            top: '5%',
            left: '25%',
            fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
            color: '#795548',
            transform: 'rotate(45deg)',
            animation: 'marketingFloat 14s ease-in-out infinite'
          }} />

          <Public sx={{
            position: 'absolute',
            top: '8%',
            right: '30%',
            fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
            color: '#009688',
            transform: 'rotate(-40deg)',
            animation: 'marketingFloat 8.5s ease-in-out infinite reverse'
          }} />

          <Share sx={{
            position: 'absolute',
            bottom: '5%',
            left: '30%',
            fontSize: { xs: '2.8rem', sm: '3.8rem', md: '4.8rem' },
            color: '#FF5722',
            transform: 'rotate(50deg)',
            animation: 'marketingFloat 10.5s ease-in-out infinite'
          }} />

          <ThumbUp sx={{
            position: 'absolute',
            bottom: '8%',
            right: '35%',
            fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
            color: '#3F51B5',
            transform: 'rotate(-35deg)',
            animation: 'marketingFloat 9.5s ease-in-out infinite reverse'
          }} />

          {/* Central Area Light Elements */}
          <Lightbulb sx={{
            position: 'absolute',
            top: '25%',
            left: '35%',
            fontSize: { xs: '2rem', sm: '3rem', md: '4rem' },
            color: '#FFC107',
            transform: 'rotate(15deg)',
            animation: 'marketingFloat 15s ease-in-out infinite',
            opacity: 0.05
          }} />

          <Psychology sx={{
            position: 'absolute',
            top: '70%',
            right: '40%',
            fontSize: { xs: '2.2rem', sm: '3.2rem', md: '4.2rem' },
            color: '#673AB7',
            transform: 'rotate(-45deg)',
            animation: 'marketingFloat 11.5s ease-in-out infinite reverse',
            opacity: 0.05
          }} />

          <Rocket sx={{
            position: 'absolute',
            top: '40%',
            left: '45%',
            fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
            color: '#FF7043',
            transform: 'rotate(60deg)',
            animation: 'marketingFloat 7.5s ease-in-out infinite',
            opacity: 0.04
          }} />

          {/* Scattered Small Stars */}
          <Star sx={{
            position: 'absolute',
            top: '12%',
            left: '15%',
            fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' },
            color: '#FFD700',
            transform: 'rotate(-60deg)',
            animation: 'marketingFloat 5s ease-in-out infinite'
          }} />

          <Star sx={{
            position: 'absolute',
            top: '80%',
            left: '20%',
            fontSize: { xs: '1.2rem', sm: '1.7rem', md: '2.2rem' },
            color: '#FFD700',
            transform: 'rotate(75deg)',
            animation: 'marketingFloat 6.5s ease-in-out infinite reverse'
          }} />

          <Star sx={{
            position: 'absolute',
            top: '18%',
            right: '20%',
            fontSize: { xs: '1.3rem', sm: '1.8rem', md: '2.3rem' },
            color: '#FFD700',
            transform: 'rotate(-75deg)',
            animation: 'marketingFloat 4.5s ease-in-out infinite'
          }} />

          <Star sx={{
            position: 'absolute',
            bottom: '20%',
            right: '25%',
            fontSize: { xs: '1.4rem', sm: '1.9rem', md: '2.4rem' },
            color: '#FFD700',
            transform: 'rotate(90deg)',
            animation: 'marketingFloat 5.5s ease-in-out infinite reverse'
          }} />
        </Box>
      <Container
        maxWidth="sm"
        sx={{
          width: '100%',
          maxWidth: { xs: '95%', sm: '480px', md: '520px', lg: '580px' },
          mx: 'auto',
          px: { xs: 1, sm: 2, md: 3 }
        }}
      >
        <Fade in timeout={1000}>
          <Card
            sx={{
              borderRadius: { xs: 2, sm: 3, md: 4, lg: 5 },
              boxShadow: {
                xs: '0 8px 20px rgba(30, 58, 138, 0.2), 0 0 0 1px rgba(59, 130, 246, 0.3)',
                sm: '0 15px 30px rgba(30, 58, 138, 0.25), 0 0 0 1px rgba(59, 130, 246, 0.4)',
                md: '0 20px 40px rgba(30, 58, 138, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.4)',
                lg: '0 25px 50px rgba(30, 58, 138, 0.35), 0 0 0 1px rgba(59, 130, 246, 0.5)'
              },
              overflow: 'hidden',
              background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
              border: '1px solid rgba(59, 130, 246, 0.4)',
              position: 'relative',
              zIndex: 1,
              width: '100%',
              maxWidth: '100%',
              // Extra small screens (phones in portrait)
              '@media (max-width: 360px)': {
                borderRadius: 1,
                mx: 0.5,
                boxShadow: '0 4px 12px rgba(30, 58, 138, 0.15)'
              },
              // Large screens
              '@media (min-width: 1200px)': {
                maxWidth: '600px'
              }
            }}
          >
            {/* Header */}
            <Box
              sx={{
                background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e3a8a 100%)',
                color: 'white',
                textAlign: 'center',
                py: { xs: 2, sm: 3, md: 4 },
                px: { xs: 2, sm: 3, md: 4 },
                position: 'relative',
                borderBottom: '1px solid rgba(255,255,255,0.2)',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',
                  pointerEvents: 'none'
                }
              }}
            >
              {/* Logo and Title Container */}
              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                alignItems: 'center',
                justifyContent: 'center',
                mb: { xs: 1, sm: 2 },
                gap: { xs: 1, sm: 2 },
                width: '100%',
                px: { xs: 1, sm: 2 }
              }}>
                {/* Academy Icon */}
                <Box sx={{
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  '&:hover .main-book': {
                    transform: 'rotate(-2deg) scale(1.05)',
                    filter: 'drop-shadow(0 0 15px rgba(255, 215, 0, 0.8))'
                  },
                  '&:hover .sparkle-stars': {
                    animation: 'sparkle 1s ease-in-out infinite'
                  }
                }}>
                  <ImportContacts
                    className="main-book"
                    sx={{
                      fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem' },
                      color: '#ffffff',
                      filter: 'drop-shadow(0 0 10px rgba(255, 255, 255, 0.6))',
                      animation: 'bookFloat 3s ease-in-out infinite alternate',
                      transform: 'rotate(-8deg)',
                      transition: 'all 0.4s ease'
                    }}
                  />
                  <AutoAwesome
                    className="sparkle-stars"
                    sx={{
                      fontSize: { xs: '1rem', sm: '1.2rem', md: '1.4rem' },
                      color: '#ffffff',
                      position: 'absolute',
                      top: '20%',
                      right: '15%',
                      filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.4))',
                      animation: 'sparkle 2s ease-in-out infinite alternate',
                      transition: 'all 0.3s ease'
                    }}
                  />
                  <AutoAwesome
                    className="sparkle-stars"
                    sx={{
                      fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' },
                      color: '#ffffff',
                      position: 'absolute',
                      bottom: '25%',
                      left: '20%',
                      filter: 'drop-shadow(0 0 3px rgba(255, 255, 255, 0.3))',
                      animation: 'sparkle 2.5s ease-in-out infinite alternate-reverse',
                      transition: 'all 0.3s ease'
                    }}
                  />
                </Box>

                {/* Academy Title */}
                <Box sx={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  overflow: 'visible'
                }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 700,
                      color: '#ffffff',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                      letterSpacing: { xs: '0.5px', sm: '1px', md: '1.5px' },
                      fontFamily: '"Roboto", "Arial", sans-serif',
                      fontSize: { xs: '1.1rem', sm: '1.5rem', md: '1.9rem' },
                      textAlign: 'center',
                      lineHeight: 1.2,
                      whiteSpace: 'nowrap',
                      display: 'inline-block',
                      minWidth: 'max-content'
                    }}
                  >
                    SKILLS WORLD ACADEMY
                  </Typography>
                </Box>
              </Box>

              {/* Instructor Name */}
              <Box sx={{
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                mb: { xs: 0.5, sm: 1 }
              }}>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 500,
                    color: 'rgba(255,255,255,0.9)',
                    letterSpacing: { xs: '1px', sm: '1.2px', md: '1.5px' },
                    fontFamily: '"Roboto", "Arial", sans-serif',
                    textShadow: '0 1px 2px rgba(0,0,0,0.2)',
                    fontSize: { xs: '1rem', sm: '1.2rem', md: '1.4rem' },
                    textAlign: 'center',
                    whiteSpace: 'nowrap',
                    display: 'inline-block',
                    minWidth: 'max-content'
                  }}
                >
                  ALAA ABD HAMIED
                </Typography>
              </Box>

              <Typography
                variant="h6"
                sx={{
                  opacity: 0.9,
                  color: 'rgba(255,255,255,0.8)',
                  fontWeight: 300,
                  letterSpacing: { xs: '0.5px', sm: '0.8px', md: '1px' },
                  fontSize: { xs: '0.85rem', sm: '0.95rem', md: '1rem' },
                  textAlign: 'center',
                  px: { xs: 1, sm: 2 },
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
              >
                {t.platformDesc}
              </Typography>

              {/* CSS Animations for book, sparkle and background effects */}
              <style>
                {`
                  @keyframes bookFloat {
                    0% {
                      transform: rotate(-8deg) translateY(0px);
                      filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.6));
                    }
                    50% {
                      transform: rotate(-5deg) translateY(-8px);
                      filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.8));
                    }
                    100% {
                      transform: rotate(-8deg) translateY(0px);
                      filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.6));
                    }
                  }

                  @keyframes sparkle {
                    0% {
                      transform: rotate(0deg) scale(1);
                      opacity: 0.7;
                    }
                    50% {
                      transform: rotate(180deg) scale(1.2);
                      opacity: 1;
                    }
                    100% {
                      transform: rotate(360deg) scale(1);
                      opacity: 0.7;
                    }
                  }

                  @keyframes float {
                    0% {
                      transform: translateY(0px) rotate(var(--rotation, 0deg));
                      opacity: 0.03;
                    }
                    50% {
                      transform: translateY(-20px) rotate(calc(var(--rotation, 0deg) + 5deg));
                      opacity: 0.05;
                    }
                    100% {
                      transform: translateY(0px) rotate(var(--rotation, 0deg));
                      opacity: 0.03;
                    }
                  }

                  @keyframes marketingFloat {
                    0% {
                      transform: translateY(0px) translateX(0px) rotate(var(--rotation, 0deg));
                      opacity: 0.08;
                    }
                    25% {
                      transform: translateY(-15px) translateX(5px) rotate(calc(var(--rotation, 0deg) + 3deg));
                      opacity: 0.12;
                    }
                    50% {
                      transform: translateY(-25px) translateX(0px) rotate(calc(var(--rotation, 0deg) + 5deg));
                      opacity: 0.15;
                    }
                    75% {
                      transform: translateY(-15px) translateX(-5px) rotate(calc(var(--rotation, 0deg) + 3deg));
                      opacity: 0.12;
                    }
                    100% {
                      transform: translateY(0px) translateX(0px) rotate(var(--rotation, 0deg));
                      opacity: 0.08;
                    }
                  }

                  @keyframes pageFlip {
                    0% { transform: rotateY(0deg); }
                    50% { transform: rotateY(15deg); }
                    100% { transform: rotateY(0deg); }
                  }

                  /* Responsive adjustments for background icons */
                  @media (max-width: 768px) {
                    .marketing-bg-icon {
                      font-size: 2rem !important;
                      opacity: 0.02 !important;
                    }
                  }

                  @media (max-width: 480px) {
                    .marketing-bg-icon {
                      font-size: 1.5rem !important;
                      opacity: 0.015 !important;
                    }
                  }
                `}
              </style>
            </Box>

            <CardContent sx={{ p: 0, background: '#000000' }}>
              {/* Tabs */}
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
                sx={{
                  borderBottom: '1px solid rgba(59, 130, 246, 0.4)',
                  background: 'linear-gradient(90deg, #1e3a8a 0%, #3b82f6 50%, #1e3a8a 100%)',
                  '& .MuiTab-root': {
                    py: 2,
                    fontSize: '1rem',
                    fontWeight: 600,
                    color: 'rgba(255,255,255,0.8)',
                    textTransform: 'none',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      color: 'white',
                      background: 'rgba(255,255,255,0.1)'
                    },
                    '&.Mui-selected': {
                      color: 'white',
                      background: 'linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%)',
                      borderBottom: '2px solid white'
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: '#ffffff',
                    height: '3px',
                    boxShadow: '0 0 8px rgba(255, 255, 255, 0.5)'
                  }
                }}
              >
                <Tab
                  icon={<AdminPanelSettings sx={{
                    color: tabValue === 0 ? '#ffffff' : 'rgba(255,255,255,0.7)',
                    filter: tabValue === 0 ? 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.3))' : 'none'
                  }} />}
                  label={t.admin}
                  iconPosition="start"
                />
                <Tab
                  icon={<School sx={{
                    color: tabValue === 1 ? '#ffffff' : 'rgba(255,255,255,0.7)',
                    filter: tabValue === 1 ? 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.3))' : 'none'
                  }} />}
                  label={t.student}
                  iconPosition="start"
                />
              </Tabs>

              <Box sx={{
                p: { xs: 1.5, sm: 2.5, md: 3.5, lg: 4 },
                background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
                minHeight: { xs: '320px', sm: '360px', md: '400px', lg: '420px' }
              }}>
                {/* Admin Login */}
                {tabValue === 0 && (
                  <Slide direction="right" in={tabValue === 0} timeout={300}>
                    <Box component="form" onSubmit={handleAdminSubmit}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          textAlign: 'center',
                          color: '#1e3a8a',
                          fontWeight: 600,
                          letterSpacing: { xs: '0.5px', sm: '1px' },
                          fontSize: { xs: '1.1rem', sm: '1.25rem' }
                        }}
                      >
                        {t.adminLogin}
                      </Typography>
                      
                      <TextField
                        fullWidth
                        label={t.email}
                        type="email"
                        value={adminForm.email}
                        onChange={handleAdminChange('email')}
                        required
                        sx={{
                          mb: { xs: 2, sm: 2.5, md: 3 },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(59, 130, 246, 0.08)',
                            border: '1px solid rgba(30, 58, 138, 0.4)',
                            borderRadius: { xs: '8px', sm: '10px', md: '12px' },
                            color: '#1f2937',
                            fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
                            fontWeight: 500,
                            minHeight: { xs: '48px', sm: '52px', md: '56px' },
                            padding: { xs: '8px 12px', sm: '10px 14px', md: '12px 16px' },
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              border: '1px solid rgba(30, 58, 138, 0.6)',
                              backgroundColor: 'rgba(59, 130, 246, 0.12)',
                              transform: { xs: 'none', sm: 'translateY(-1px)' }
                            },
                            '&.Mui-focused': {
                              border: '1px solid #1e3a8a',
                              backgroundColor: 'rgba(59, 130, 246, 0.15)',
                              boxShadow: {
                                xs: '0 0 0 1px rgba(30, 58, 138, 0.3)',
                                sm: '0 0 0 2px rgba(30, 58, 138, 0.3)',
                                md: '0 0 0 2px rgba(30, 58, 138, 0.35)'
                              },
                              transform: 'translateY(-1px)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: '#374151',
                            fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                            fontWeight: 600,
                            transform: { xs: 'translate(12px, 14px) scale(1)', sm: 'translate(14px, 16px) scale(1)', md: 'translate(16px, 18px) scale(1)' },
                            '&.Mui-focused': {
                              color: '#1f2937'
                            },
                            '&.MuiInputLabel-shrink': {
                              transform: { xs: 'translate(12px, -6px) scale(0.75)', sm: 'translate(14px, -6px) scale(0.75)', md: 'translate(16px, -6px) scale(0.75)' }
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          },
                          '& .MuiInputBase-input': {
                            padding: { xs: '12px 8px', sm: '14px 10px', md: '16px 12px' }
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <AdminPanelSettings sx={{
                                color: '#FFD700',
                                filter: 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))'
                              }} />
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <TextField
                        fullWidth
                        label={t.password}
                        type={showPassword ? 'text' : 'password'}
                        value={adminForm.password}
                        onChange={handleAdminChange('password')}
                        required
                        sx={{
                          mb: 4,
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(59, 130, 246, 0.08)',
                            border: '1px solid rgba(30, 58, 138, 0.4)',
                            borderRadius: '12px',
                            color: '#1f2937',
                            fontSize: '1.1rem',
                            fontWeight: 500,
                            '&:hover': {
                              border: '1px solid rgba(30, 58, 138, 0.6)',
                              backgroundColor: 'rgba(59, 130, 246, 0.12)'
                            },
                            '&.Mui-focused': {
                              border: '1px solid #1e3a8a',
                              backgroundColor: 'rgba(59, 130, 246, 0.15)',
                              boxShadow: '0 0 0 2px rgba(30, 58, 138, 0.3)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: '#374151',
                            fontSize: '1rem',
                            fontWeight: 600,
                            '&.Mui-focused': {
                              color: '#1f2937'
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          }
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowPassword(!showPassword)}
                                edge="end"
                                sx={{
                                  color: '#374151',
                                  '&:hover': {
                                    color: '#1f2937',
                                    background: 'rgba(59, 130, 246, 0.1)'
                                  }
                                }}
                              >
                                {showPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <Button
                        type="submit"
                        fullWidth
                        variant="contained"
                        size="large"
                        disabled={loading}
                        startIcon={loading ? <CircularProgress size={20} sx={{ color: '#FFD700' }} /> : <LoginIcon sx={{ color: '#FFD700' }} />}
                        sx={{
                          py: { xs: 1.2, sm: 1.4, md: 1.5 },
                          px: { xs: 2, sm: 3, md: 4 },
                          fontSize: { xs: '0.95rem', sm: '1rem', md: '1.1rem' },
                          fontWeight: 600,
                          background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)',
                          color: '#ffffff',
                          border: '1px solid rgba(30, 58, 138, 0.4)',
                          borderRadius: { xs: '8px', sm: '10px', md: '12px' },
                          textTransform: 'none',
                          boxShadow: {
                            xs: '0 2px 8px rgba(255,255,255,0.15)',
                            sm: '0 3px 12px rgba(255,255,255,0.18)',
                            md: '0 4px 15px rgba(255,255,255,0.2)'
                          },
                          transition: 'all 0.3s ease',
                          minHeight: { xs: '44px', sm: '48px', md: '52px' },
                          width: '100%',
                          maxWidth: '100%',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%)',
                            transform: { xs: 'translateY(-1px)', sm: 'translateY(-1.5px)', md: 'translateY(-2px)' },
                            boxShadow: {
                              xs: '0 4px 12px rgba(30, 58, 138, 0.3)',
                              sm: '0 5px 16px rgba(30, 58, 138, 0.35)',
                              md: '0 6px 20px rgba(30, 58, 138, 0.4)'
                            }
                          },
                          '&:active': {
                            transform: 'translateY(0px)',
                            boxShadow: {
                              xs: '0 1px 4px rgba(30, 58, 138, 0.15)',
                              sm: '0 2px 6px rgba(30, 58, 138, 0.18)',
                              md: '0 2px 8px rgba(30, 58, 138, 0.2)'
                            }
                          },
                          '&:disabled': {
                            background: 'rgba(30, 58, 138, 0.3)',
                            color: 'rgba(255,255,255,0.5)',
                            transform: 'none',
                            boxShadow: 'none'
                          },
                          // Touch-friendly for mobile
                          '@media (hover: none)': {
                            '&:hover': {
                              transform: 'none',
                              background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)'
                            }
                          }
                        }}
                      >
                        {loading ? t.loggingIn : t.login}
                      </Button>
                    </Box>
                  </Slide>
                )}

                {/* Student Login */}
                {tabValue === 1 && (
                  <Slide direction="left" in={tabValue === 1} timeout={300}>
                    <Box component="form" onSubmit={handleStudentSubmit}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          textAlign: 'center',
                          color: '#1e3a8a',
                          fontWeight: 600,
                          letterSpacing: { xs: '0.5px', sm: '1px' },
                          fontSize: { xs: '1.1rem', sm: '1.25rem' }
                        }}
                      >
                        {t.studentLogin}
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          textAlign: 'center',
                          color: '#374151',
                          fontSize: { xs: '0.85rem', sm: '0.95rem' },
                          px: { xs: 1, sm: 0 },
                          fontWeight: 500
                        }}
                      >
                        {t.studentCodeDesc}
                      </Typography>
                      
                      <TextField
                        fullWidth
                        label={t.studentCode}
                        value={studentForm.code}
                        onChange={handleStudentChange}
                        required
                        inputProps={{
                          maxLength: 6,
                          style: {
                            textAlign: 'center',
                            fontSize: window.innerWidth < 600 ? '1.3rem' : '1.6rem',
                            letterSpacing: window.innerWidth < 600 ? '0.3rem' : '0.5rem',
                            color: '#1f2937',
                            fontWeight: 600
                          }
                        }}
                        sx={{
                          mb: { xs: 3, sm: 4 },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(59, 130, 246, 0.08)',
                            border: '1px solid rgba(30, 58, 138, 0.4)',
                            borderRadius: { xs: '8px', sm: '10px', md: '12px' },
                            color: '#1f2937',
                            minHeight: { xs: '56px', sm: '64px' },
                            '&:hover': {
                              border: '1px solid rgba(30, 58, 138, 0.6)',
                              backgroundColor: 'rgba(59, 130, 246, 0.12)'
                            },
                            '&.Mui-focused': {
                              border: '1px solid #1e3a8a',
                              backgroundColor: 'rgba(59, 130, 246, 0.15)',
                              boxShadow: '0 0 0 2px rgba(30, 58, 138, 0.3)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: '#374151',
                            fontSize: { xs: '1rem', sm: '1.1rem' },
                            fontWeight: 600,
                            '&.Mui-focused': {
                              color: '#1f2937'
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <School sx={{
                                color: '#FFD700',
                                filter: 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))'
                              }} />
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <Button
                        type="submit"
                        fullWidth
                        variant="contained"
                        size="large"
                        disabled={loading || studentForm.code.length !== 6}
                        startIcon={loading ? <CircularProgress size={20} sx={{ color: '#FFD700' }} /> : <LoginIcon sx={{ color: '#FFD700' }} />}
                        sx={{
                          py: { xs: 1.2, sm: 1.4, md: 1.5 },
                          px: { xs: 2, sm: 3, md: 4 },
                          fontSize: { xs: '0.95rem', sm: '1rem', md: '1.1rem' },
                          fontWeight: 600,
                          background: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)',
                          color: '#ffffff',
                          border: '1px solid rgba(30, 58, 138, 0.4)',
                          borderRadius: { xs: '8px', sm: '10px', md: '12px' },
                          textTransform: 'none',
                          boxShadow: {
                            xs: '0 2px 8px rgba(255,255,255,0.15)',
                            sm: '0 3px 12px rgba(255,255,255,0.18)',
                            md: '0 4px 15px rgba(255,255,255,0.2)'
                          },
                          transition: 'all 0.3s ease',
                          minHeight: { xs: '44px', sm: '48px', md: '52px' },
                          width: '100%',
                          maxWidth: '100%',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%)',
                            transform: { xs: 'translateY(-1px)', sm: 'translateY(-1.5px)', md: 'translateY(-2px)' },
                            boxShadow: {
                              xs: '0 4px 12px rgba(255,255,255,0.25)',
                              sm: '0 5px 16px rgba(255,255,255,0.28)',
                              md: '0 6px 20px rgba(255,255,255,0.3)'
                            }
                          },
                          '&:active': {
                            transform: 'translateY(0px)',
                            boxShadow: {
                              xs: '0 1px 4px rgba(255,255,255,0.1)',
                              sm: '0 2px 6px rgba(255,255,255,0.12)',
                              md: '0 2px 8px rgba(255,255,255,0.15)'
                            }
                          },
                          '&:disabled': {
                            background: 'rgba(255,255,255,0.3)',
                            color: 'rgba(0,0,0,0.5)',
                            transform: 'none',
                            boxShadow: 'none'
                          },
                          // Touch-friendly for mobile
                          '@media (hover: none)': {
                            '&:hover': {
                              transform: 'none',
                              background: 'linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%)'
                            }
                          }
                        }}
                      >
                        {loading ? t.loggingIn : t.login}
                      </Button>
                    </Box>
                  </Slide>
                )}
              </Box>
            </CardContent>
          </Card>
        </Fade>
      </Container>
      </Box>
    </Box>
  );
};

export default Login;
