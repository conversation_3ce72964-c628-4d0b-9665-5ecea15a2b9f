import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper
} from '@mui/material';
import {
  PlayArrow,
  School,
  VideoLibrary,
  WorkspacePremium,
  Logout,
  AccountCircle,
  CheckCircle,
  ExpandMore,
  Edit,
  Save,
  Cancel,
  Star,
  TrendingUp,
  Support,
  ExitToApp,
  Help,
  Close
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import CourseViewer from './CourseViewer';
import ContactAdmin from './ContactAdmin';
import SmartAssistant from './SmartAssistant';
import { getUserData, updateUserData } from '../utils/localStorageService';
import toast from 'react-hot-toast';

// مكون الملف الشخصي
const ProfileDialog = ({ open, onClose, user }) => {
  const { updateUser } = useAuth();
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || 'الطالب',
    email: user?.email || '',
    phone: user?.phone || '',
    bio: user?.bio || 'طالب في منصة SKILLS WORLD ACADEMY'
  });

  // جلب بيانات المستخدم عند فتح النافذة
  useEffect(() => {
    const loadUserProfile = async () => {
      if (open && user?.id) {
        try {
          setLoading(true);

          // جلب البيانات من localStorage أولاً باستخدام الخدمة المحسنة
          let userData = getUserData(user.id) || user;

          console.log('📥 تم جلب البيانات المحلية:', userData);

          // تخطي Firebase مؤقتاً حتى يتم إعداد المصادقة بشكل صحيح
          console.log('⚠️ تخطي Firebase مؤقتاً، استخدام البيانات المحلية فقط');

          setProfileData({
            name: userData.name || 'الطالب',
            email: userData.email || '',
            phone: userData.phone || '',
            bio: userData.bio || 'طالب في منصة SKILLS WORLD ACADEMY'
          });

          console.log('✅ تم تحميل بيانات الملف الشخصي بنجاح');

        } catch (error) {
          console.error('❌ خطأ في جلب بيانات المستخدم:', error);
          // استخدام البيانات الأساسية في حالة الخطأ
          setProfileData({
            name: user.name || 'الطالب',
            email: user.email || '',
            phone: '',
            bio: 'طالب في منصة SKILLS WORLD ACADEMY'
          });
        } finally {
          setLoading(false);
        }
      }
    };

    loadUserProfile();
  }, [open, user]);

  const handleSave = async () => {
    if (!user?.id) {
      console.error('معرف المستخدم غير متوفر');
      toast.error('خطأ: معرف المستخدم غير متوفر');
      return;
    }

    try {
      setLoading(true);

      const updatedData = {
        ...user,
        name: profileData.name,
        email: profileData.email,
        phone: profileData.phone,
        bio: profileData.bio
      };

      // حفظ البيانات محلياً أولاً (مضمون)
      const localSaveSuccess = updateUserData(user.id, updatedData);

      if (!localSaveSuccess) {
        throw new Error('فشل في حفظ البيانات محلياً');
      }

      // تحديث بيانات المستخدم في AuthContext
      updateUser(updatedData);

      // تخطي Firebase مؤقتاً
      console.log('✅ تم حفظ البيانات محلياً بنجاح');

      setEditMode(false);

      // إظهار رسالة نجاح
      toast.success('تم حفظ البيانات بنجاح!');
      console.log('✅ تم حفظ البيانات بنجاح');

    } catch (error) {
      console.error('❌ خطأ في حفظ البيانات:', error);
      toast.error('حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{
        background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AccountCircle sx={{ color: '#FFD700' }} />
          <Typography variant="h6">الملف الشخصي</Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
            <CircularProgress sx={{ color: '#FFD700' }} />
            <Typography sx={{ ml: 2 }}>جاري تحميل البيانات...</Typography>
          </Box>
        ) : (
          <>
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Avatar sx={{
                width: 80,
                height: 80,
                bgcolor: '#FFD700',
                color: '#000',
                mx: 'auto',
                mb: 2,
                fontSize: '2rem'
              }}>
                {profileData.name.charAt(0)}
              </Avatar>

          {editMode ? (
            <TextField
              fullWidth
              label="الاسم"
              value={profileData.name}
              onChange={(e) => setProfileData({...profileData, name: e.target.value})}
              sx={{ mb: 2 }}
            />
          ) : (
            <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#0000FF' }}>
              {profileData.name}
            </Typography>
          )}
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            {editMode ? (
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                value={profileData.email}
                onChange={(e) => setProfileData({...profileData, email: e.target.value})}
              />
            ) : (
              <Box>
                <Typography variant="subtitle2" color="textSecondary">البريد الإلكتروني</Typography>
                <Typography variant="body1">{profileData.email || 'غير محدد'}</Typography>
              </Box>
            )}
          </Grid>

          <Grid item xs={12}>
            {editMode ? (
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={profileData.phone}
                onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
              />
            ) : (
              <Box>
                <Typography variant="subtitle2" color="textSecondary">رقم الهاتف</Typography>
                <Typography variant="body1">{profileData.phone || 'غير محدد'}</Typography>
              </Box>
            )}
          </Grid>

          <Grid item xs={12}>
            {editMode ? (
              <TextField
                fullWidth
                multiline
                rows={3}
                label="نبذة شخصية"
                value={profileData.bio}
                onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
              />
            ) : (
              <Box>
                <Typography variant="subtitle2" color="textSecondary">نبذة شخصية</Typography>
                <Typography variant="body1">{profileData.bio}</Typography>
              </Box>
            )}
          </Grid>
        </Grid>
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        {editMode ? (
          <>
            <Button
              onClick={() => setEditMode(false)}
              startIcon={<Cancel />}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              onClick={handleSave}
              variant="contained"
              startIcon={loading ? <CircularProgress size={16} /> : <Save />}
              disabled={loading}
              sx={{
                background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
                color: '#000',
                '&:disabled': {
                  background: 'rgba(255, 215, 0, 0.3)',
                  color: 'rgba(0, 0, 0, 0.5)'
                }
              }}
            >
              {loading ? 'جاري الحفظ...' : 'حفظ'}
            </Button>
          </>
        ) : (
          <Button
            onClick={() => setEditMode(true)}
            variant="contained"
            startIcon={<Edit />}
            disabled={loading}
            sx={{
              background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
              '&:disabled': {
                background: 'rgba(0, 0, 255, 0.3)'
              }
            }}
          >
            تعديل
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

// مكون الأسئلة الشائعة
const FAQDialog = ({ open, onClose }) => {
  const faqData = [
    {
      question: 'كيف يمكنني الوصول إلى الكورسات؟',
      answer: 'يمكنك الوصول إلى جميع الكورسات المسجل بها من خلال لوحة التحكم الرئيسية. اضغط على "متابعة التعلم" للبدء.'
    },
    {
      question: 'كيف أتابع تقدمي في الكورس؟',
      answer: 'يتم عرض نسبة التقدم في كل كورس على شكل شريط تقدم. كما يمكنك رؤية عدد الفيديوهات المكتملة من إجمالي الفيديوهات.'
    },
    {
      question: 'كيف أحصل على الشهادة؟',
      answer: 'بعد إكمال جميع فيديوهات الكورس بنسبة 100%، ستظهر لك خيار "تحميل الشهادة" في بطاقة الكورس.'
    },
    {
      question: 'كيف أتواصل مع المدرب؟',
      answer: 'يمكنك التواصل مع المدرب علاء عبد الحميد من خلال زر "تواصل مع المدير" أو عبر الهاتف: 0506747770'
    },
    {
      question: 'ماذا لو واجهت مشكلة تقنية؟',
      answer: 'في حالة مواجهة أي مشكلة تقنية، تواصل مع الدعم الفني عبر نافذة "تواصل مع المدير" وسيتم حل المشكلة في أسرع وقت.'
    },
    {
      question: 'هل يمكنني مشاهدة الفيديوهات أكثر من مرة؟',
      answer: 'نعم، يمكنك مشاهدة أي فيديو عدة مرات حسب حاجتك للمراجعة والفهم الأفضل.'
    }
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{
        background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Help sx={{ color: '#FFD700' }} />
          <Typography variant="h6">الأسئلة الشائعة</Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {faqData.map((faq, index) => (
          <Accordion key={index}>
            <AccordionSummary
              expandIcon={<ExpandMore />}
              sx={{
                bgcolor: index % 2 === 0 ? '#f8f9fa' : 'white',
                '&:hover': {
                  bgcolor: 'rgba(0, 0, 255, 0.05)'
                }
              }}
            >
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#0000FF' }}>
                {faq.question}
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ bgcolor: 'white' }}>
              <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                {faq.answer}
              </Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </DialogContent>

      <DialogActions sx={{ p: 2, bgcolor: '#f8f9fa' }}>
        <Typography variant="body2" color="textSecondary" sx={{ flexGrow: 1 }}>
          لم تجد إجابة لسؤالك؟ تواصل مع الدعم الفني
        </Typography>
        <Button onClick={onClose} variant="contained" sx={{
          background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
          color: '#000'
        }}>
          إغلاق
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const StudentDashboard = () => {
  const { user, logout } = useAuth();
  const [anchorEl, setAnchorEl] = useState(null);
  const [courses, setCourses] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [openContactDialog, setOpenContactDialog] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showFAQ, setShowFAQ] = useState(false);
  const [stats, setStats] = useState({
    enrolledCourses: 0,
    completedVideos: 0,
    totalVideos: 0,
    certificates: 0
  });

  useEffect(() => {
    fetchStudentData();
  }, []);

  const fetchStudentData = async () => {
    try {
      // محاولة جلب البيانات من Firebase أو API
      console.log('🔄 جاري جلب بيانات الطالب...');

      // استخدام البيانات الافتراضية مباشرة (لأن API غير متوفر حالياً)
      const defaultCourses = [
        {
          _id: '1',
          title: 'أساسيات التسويق الرقمي',
          description: 'تعلم أساسيات التسويق الرقمي من الصفر',
          progress: 67,
          totalVideos: 12,
          completedVideos: 8,
          instructor: 'علاء عبد الحميد',
          rating: 4.8,
          isEnrolled: true,
          level: 'مبتدئ',
          duration: '8 ساعات'
        },
        {
          _id: '2',
          title: 'إدارة وسائل التواصل الاجتماعي',
          description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية',
          progress: 30,
          totalVideos: 10,
          completedVideos: 3,
          instructor: 'علاء عبد الحميد',
          rating: 4.9,
          isEnrolled: true,
          level: 'متوسط',
          duration: '6 ساعات'
        }
      ];

      const defaultStats = {
        enrolledCourses: 2,
        completedVideos: 11,
        totalVideos: 22,
        certificates: 0,
        overallProgress: 50
      };

      setCourses(defaultCourses);
      setStats(defaultStats);

      console.log('✅ تم تحميل البيانات الافتراضية بنجاح');

    } catch (error) {
      console.error('❌ خطأ في جلب بيانات الطالب:', error);

      // بيانات احتياطية في حالة الخطأ
      setCourses([]);
      setStats({
        enrolledCourses: 0,
        completedVideos: 0,
        totalVideos: 0,
        certificates: 0,
        overallProgress: 0
      });
    }
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleMenuClose();
  };

  const getProgressColor = (progress) => {
    if (progress === 0) return 'error';
    if (progress < 50) return 'warning';
    if (progress < 100) return 'info';
    return 'success';
  };

  const handleCourseClick = (course) => {
    setSelectedCourse(course);
  };

  const handleBackToDashboard = () => {
    setSelectedCourse(null);
    fetchStudentData(); // إعادة تحميل البيانات
  };

  // إذا تم اختيار كورس، اعرض CourseViewer
  if (selectedCourse) {
    return <CourseViewer course={selectedCourse} onBack={handleBackToDashboard} />;
  }

  return (
    <Box sx={{ flexGrow: 1, bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <AppBar position="static" sx={{ bgcolor: '#1976d2' }}>
        <Toolbar>
          <School sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            منصة علاء عبد الحميد - لوحة الطالب
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2">
              مرحباً، {user?.name || 'الطالب'}
            </Typography>

            {/* زر التواصل مع المدير */}
            <Button
              variant="outlined"
              size="small"
              startIcon={<Support />}
              onClick={() => setOpenContactDialog(true)}
              sx={{
                color: 'white',
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255,255,255,0.1)'
                }
              }}
            >
              تواصل مع المدير
            </Button>

            {/* زر تسجيل الخروج */}
            <Button
              variant="contained"
              size="small"
              startIcon={<ExitToApp />}
              onClick={handleLogout}
              sx={{
                bgcolor: '#f44336',
                '&:hover': {
                  bgcolor: '#d32f2f'
                }
              }}
            >
              تسجيل الخروج
            </Button>

            <IconButton
              size="large"
              edge="end"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenuOpen}
              color="inherit"
            >
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                <AccountCircle />
              </Avatar>
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={() => {
                setShowProfile(true);
                handleMenuClose();
              }}>
                <AccountCircle sx={{ mr: 1 }} />
                الملف الشخصي
              </MenuItem>
              <MenuItem onClick={() => {
                setOpenContactDialog(true);
                handleMenuClose();
              }}>
                <Support sx={{ mr: 1 }} />
                تواصل مع المدير
              </MenuItem>
              <MenuItem onClick={() => {
                setShowFAQ(true);
                handleMenuClose();
              }}>
                <Help sx={{ mr: 1 }} />
                الأسئلة الشائعة
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <Logout sx={{ mr: 1 }} />
                تسجيل الخروج
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      <Box sx={{ p: 3 }}>
        {/* إحصائيات سريعة */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#e3f2fd' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <School sx={{ fontSize: 40, color: '#1976d2', mr: 2 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                      {stats.enrolledCourses}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      الدورات المسجلة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#e8f5e8' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <VideoLibrary sx={{ fontSize: 40, color: '#4caf50', mr: 2 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                      {stats.completedVideos}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      الفيديوهات المكتملة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#fff3e0' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUp sx={{ fontSize: 40, color: '#ff9800', mr: 2 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                      {Math.round((stats.completedVideos / stats.totalVideos) * 100)}%
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      نسبة الإنجاز
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: '#f3e5f5' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <WorkspacePremium sx={{ fontSize: 40, color: '#9c27b0', mr: 2 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>
                      {stats.certificates}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      الشهادات المحصلة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* الدورات */}
        <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 3 }}>
          دوراتي
        </Typography>

        <Grid container spacing={3}>
          {courses.map((course) => (
            <Grid item xs={12} md={6} lg={4} key={course.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Box
                  sx={{
                    height: 200,
                    bgcolor: '#f0f0f0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <VideoLibrary sx={{ fontSize: 60, color: '#ccc' }} />
                </Box>

                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {course.title}
                  </Typography>

                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                    {course.description}
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Star sx={{ color: '#ffc107', fontSize: 16, mr: 0.5 }} />
                    <Typography variant="body2" sx={{ mr: 2 }}>
                      {course.rating}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {course.instructor}
                    </Typography>
                  </Box>

                  {course.isEnrolled ? (
                    <>
                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2">
                            التقدم: {course.completedVideos}/{course.totalVideos}
                          </Typography>
                          <Typography variant="body2">
                            {course.progress}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={course.progress}
                          color={getProgressColor(course.progress)}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </Box>

                      <Button
                        fullWidth
                        variant="contained"
                        startIcon={<PlayArrow />}
                        sx={{ mb: 1 }}
                        onClick={() => handleCourseClick(course)}
                      >
                        متابعة التعلم
                      </Button>

                      {course.progress === 100 && (
                        <Button
                          fullWidth
                          variant="outlined"
                          startIcon={<WorkspacePremium />}
                          color="secondary"
                        >
                          تحميل الشهادة
                        </Button>
                      )}
                    </>
                  ) : (
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<School />}
                    >
                      التسجيل في الدورة
                    </Button>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* النشاط الأخير */}
        <Paper sx={{ mt: 4, p: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            النشاط الأخير
          </Typography>

          <List>
            <ListItem>
              <ListItemIcon>
                <CheckCircle sx={{ color: '#4caf50' }} />
              </ListItemIcon>
              <ListItemText
                primary="تم إكمال فيديو: مقدمة في التسويق الرقمي"
                secondary="منذ ساعتين"
              />
            </ListItem>

            <Divider />

            <ListItem>
              <ListItemIcon>
                <PlayArrow sx={{ color: '#2196f3' }} />
              </ListItemIcon>
              <ListItemText
                primary="بدء مشاهدة: استراتيجيات التسويق"
                secondary="منذ 4 ساعات"
              />
            </ListItem>

            <Divider />

            <ListItem>
              <ListItemIcon>
                <School sx={{ color: '#ff9800' }} />
              </ListItemIcon>
              <ListItemText
                primary="التسجيل في دورة: إدارة وسائل التواصل الاجتماعي"
                secondary="أمس"
              />
            </ListItem>
          </List>
        </Paper>

        {/* قسم المساعدة والدعم */}
        <Paper sx={{ mt: 4, p: 3, bgcolor: '#f8f9fa' }}>
          <Box sx={{ textAlign: 'center' }}>
            <Support sx={{ fontSize: 48, color: '#1976d2', mb: 2 }} />
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
              هل تحتاج مساعدة؟
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
              فريق الدعم متاح لمساعدتك في أي وقت. لا تتردد في التواصل معنا!
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                startIcon={<Support />}
                onClick={() => setOpenContactDialog(true)}
                sx={{ borderRadius: 3 }}
              >
                تواصل مع المدير
              </Button>

              <Button
                variant="outlined"
                startIcon={<Help />}
                onClick={() => setShowFAQ(true)}
                sx={{ borderRadius: 3 }}
              >
                الأسئلة الشائعة
              </Button>
            </Box>
          </Box>
        </Paper>

        {/* مكون التواصل مع المدير */}
        <ContactAdmin
          open={openContactDialog}
          onClose={() => setOpenContactDialog(false)}
        />

        {/* المساعد الذكي */}
        <SmartAssistant language="ar" />
      </Box>

      {/* نافذة الملف الشخصي */}
      <ProfileDialog
        open={showProfile}
        onClose={() => setShowProfile(false)}
        user={user}
      />

      {/* نافذة الأسئلة الشائعة */}
      <FAQDialog
        open={showFAQ}
        onClose={() => setShowFAQ(false)}
      />
    </Box>
  );
};

export default StudentDashboard;
