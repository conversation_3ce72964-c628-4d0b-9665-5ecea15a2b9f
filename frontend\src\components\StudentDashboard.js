import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  CircularProgress,

  Paper
} from '@mui/material';
import {
  PlayArrow,
  School,
  VideoLibrary,
  WorkspacePremium,
  Logout,
  AccountCircle,

  ExpandMore,
  Edit,
  Save,
  Cancel,
  Star,
  TrendingUp,
  Support,
  ExitToApp,
  Help,
  Close,
  Refresh
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import CourseViewer from './CourseViewer';
import ContactAdmin from './ContactAdmin';
import SmartAssistant from './SmartAssistant';
import {
  saveUserProfile,
  getUserProfile as getFirebaseProfile,
  updateUserProfile as updateFirebaseProfile,
  subscribeToUserProfile,
  ensureUserProfile
} from '../firebase/profileService';
import {
  getStudentStats,
  getStudentCourses
} from '../firebase/studentDataService';
import { getUserData, updateUserData, saveUserData } from '../utils/localStorageService';
import toast from 'react-hot-toast';

// مكون الملف الشخصي
const ProfileDialog = ({ open, onClose, user }) => {
  const { updateUser } = useAuth();
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || 'الطالب',
    email: user?.email || '',
    phone: user?.phone || '',
    bio: user?.bio || 'طالب في منصة SKILLS WORLD ACADEMY'
  });

  // جلب بيانات المستخدم عند فتح النافذة
  useEffect(() => {
    let unsubscribe = null;

    const loadUserProfile = async () => {
      if (open && user?.id) {
        try {
          setLoading(true);
          console.log('📥 بدء تحميل الملف الشخصي...', user.id);

          // جلب البيانات من localStorage أولاً
          let userData = getUserData(user.id) || user;
          console.log('📥 البيانات المحلية:', userData);

          // تحديث الواجهة بالبيانات المحلية فوراً
          setProfileData({
            name: userData.name || 'الطالب',
            email: userData.email || '',
            phone: userData.phone || '',
            bio: userData.bio || 'طالب في منصة SKILLS WORLD ACADEMY'
          });

          setLoading(false); // إنهاء التحميل هنا

          // محاولة الاتصال بـ Firebase في الخلفية (بدون انتظار)
          try {
            console.log('🔄 محاولة الاتصال بـ Firebase...');

            // إعداد الاستماع للتغييرات (اختياري)
            unsubscribe = subscribeToUserProfile(user.id, (firebaseProfile) => {
              if (firebaseProfile) {
                console.log('🔄 تحديث من Firebase:', firebaseProfile);

                const updatedData = {
                  ...userData,
                  ...firebaseProfile,
                  id: user.id
                };

                // تحديث البيانات المحلية
                saveUserData(user.id, updatedData);

                // تحديث الواجهة
                setProfileData({
                  name: updatedData.name || 'الطالب',
                  email: updatedData.email || '',
                  phone: updatedData.phone || '',
                  bio: updatedData.bio || 'طالب في منصة SKILLS WORLD ACADEMY'
                });

                // تحديث AuthContext
                updateUser(updatedData);
              }
            });

            // التأكد من وجود الملف الشخصي (في الخلفية)
            ensureUserProfile(user.id, {
              name: userData.name || 'الطالب',
              email: userData.email || '',
              phone: userData.phone || '',
              bio: userData.bio || 'طالب في منصة SKILLS WORLD ACADEMY',
              studentCode: userData.studentCode || '',
              role: userData.role || 'student'
            }).catch(error => {
              console.log('⚠️ Firebase غير متاح:', error.message);
            });

          } catch (firebaseError) {
            console.log('⚠️ Firebase غير متاح، استخدام البيانات المحلية فقط:', firebaseError.message);
          }

        } catch (error) {
          console.error('❌ خطأ في تحميل الملف الشخصي:', error);

          // استخدام البيانات الأساسية في حالة الخطأ
          setProfileData({
            name: user.name || 'الطالب',
            email: user.email || '',
            phone: '',
            bio: 'طالب في منصة SKILLS WORLD ACADEMY'
          });

          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    if (open) {
      loadUserProfile();
    }

    // تنظيف الاستماع عند إغلاق النافذة
    return () => {
      if (unsubscribe) {
        console.log('🧹 تنظيف الاستماع للملف الشخصي');
        unsubscribe();
      }
    };
  }, [open, user?.id]); // إزالة updateUser من dependencies

  const handleSave = async () => {
    if (!user?.id) {
      console.error('معرف المستخدم غير متوفر');
      toast.error('خطأ: معرف المستخدم غير متوفر');
      return;
    }

    try {
      setSaving(true);

      const updatedData = {
        ...user,
        name: profileData.name,
        email: profileData.email,
        phone: profileData.phone,
        bio: profileData.bio
      };

      console.log('💾 بدء حفظ الملف الشخصي...', updatedData);

      // حفظ البيانات محلياً أولاً (مضمون وسريع)
      const localSaveSuccess = updateUserData(user.id, updatedData);

      if (!localSaveSuccess) {
        throw new Error('فشل في حفظ البيانات محلياً');
      }

      // تحديث AuthContext فوراً
      updateUser(updatedData);

      // إظهار رسالة نجاح فوراً
      toast.success('تم حفظ البيانات بنجاح!');
      setEditMode(false);

      // محاولة حفظ في Firebase في الخلفية (بدون انتظار)
      saveUserProfile(user.id, {
        name: profileData.name,
        email: profileData.email,
        phone: profileData.phone,
        bio: profileData.bio,
        studentCode: user.studentCode,
        role: user.role || 'student'
      }).then(() => {
        console.log('✅ تم حفظ البيانات في Firebase بنجاح');
      }).catch((firebaseError) => {
        console.log('⚠️ Firebase غير متاح، البيانات محفوظة محلياً:', firebaseError.message);
      });

    } catch (error) {
      console.error('❌ خطأ في حفظ البيانات:', error);
      toast.error('حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      fullScreen={false}
      sx={{
        '& .MuiDialog-paper': {
          margin: { xs: 1, sm: 2 },
          width: { xs: 'calc(100% - 16px)', sm: 'auto' },
          maxHeight: { xs: 'calc(100% - 16px)', sm: 'calc(100% - 64px)' }
        }
      }}
    >
      <DialogTitle sx={{
        background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        p: { xs: 2, sm: 3 }
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AccountCircle sx={{ color: '#FFD700' }} />
          <Typography variant="h6">الملف الشخصي</Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: { xs: 2, sm: 3 } }}>
        {loading ? (
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: { xs: 150, sm: 200 },
            flexDirection: { xs: 'column', sm: 'row' },
            gap: 2
          }}>
            <CircularProgress sx={{ color: '#FFD700' }} />
            <Typography sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}>
              جاري تحميل البيانات...
            </Typography>
          </Box>
        ) : (
          <>
            <Box sx={{ textAlign: 'center', mb: { xs: 2, sm: 3 } }}>
              <Avatar sx={{
                width: { xs: 60, sm: 80 },
                height: { xs: 60, sm: 80 },
                bgcolor: '#FFD700',
                color: '#000',
                mx: 'auto',
                mb: 2,
                fontSize: { xs: '1.5rem', sm: '2rem' }
              }}>
                {profileData.name.charAt(0)}
              </Avatar>

          {editMode ? (
            <TextField
              fullWidth
              label="الاسم"
              value={profileData.name}
              onChange={(e) => setProfileData({...profileData, name: e.target.value})}
              sx={{
                mb: 2,
                '& .MuiInputBase-root': {
                  fontSize: { xs: '0.9rem', sm: '1rem' }
                }
              }}
            />
          ) : (
            <Typography
              variant="h5"
              sx={{
                fontWeight: 'bold',
                color: '#0000FF',
                fontSize: { xs: '1.25rem', sm: '1.5rem' }
              }}
            >
              {profileData.name}
            </Typography>
          )}
        </Box>

        <Grid container spacing={{ xs: 2, sm: 2 }}>
          <Grid item xs={12}>
            {editMode ? (
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                value={profileData.email}
                onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }
                }}
              />
            ) : (
              <Box>
                <Typography
                  variant="subtitle2"
                  color="textSecondary"
                  sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                >
                  البريد الإلكتروني
                </Typography>
                <Typography
                  variant="body1"
                  sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
                >
                  {profileData.email || 'غير محدد'}
                </Typography>
              </Box>
            )}
          </Grid>

          <Grid item xs={12}>
            {editMode ? (
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={profileData.phone}
                onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
              />
            ) : (
              <Box>
                <Typography variant="subtitle2" color="textSecondary">رقم الهاتف</Typography>
                <Typography variant="body1">{profileData.phone || 'غير محدد'}</Typography>
              </Box>
            )}
          </Grid>

          <Grid item xs={12}>
            {editMode ? (
              <TextField
                fullWidth
                multiline
                rows={3}
                label="نبذة شخصية"
                value={profileData.bio}
                onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
              />
            ) : (
              <Box>
                <Typography variant="subtitle2" color="textSecondary">نبذة شخصية</Typography>
                <Typography variant="body1">{profileData.bio}</Typography>
              </Box>
            )}
          </Grid>
        </Grid>
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        {editMode ? (
          <>
            <Button
              onClick={() => setEditMode(false)}
              startIcon={<Cancel />}
              disabled={saving || loading}
            >
              إلغاء
            </Button>
            <Button
              onClick={handleSave}
              variant="contained"
              startIcon={saving ? <CircularProgress size={16} /> : <Save />}
              disabled={saving || loading}
              sx={{
                background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
                color: '#000',
                '&:disabled': {
                  background: 'rgba(255, 215, 0, 0.3)',
                  color: 'rgba(0, 0, 0, 0.5)'
                }
              }}
            >
              {saving ? 'جاري الحفظ...' : 'حفظ'}
            </Button>
          </>
        ) : (
          <Button
            onClick={() => setEditMode(true)}
            variant="contained"
            startIcon={<Edit />}
            disabled={loading}
            sx={{
              background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
              '&:disabled': {
                background: 'rgba(0, 0, 255, 0.3)'
              }
            }}
          >
            تعديل
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

// مكون الأسئلة الشائعة
const FAQDialog = ({ open, onClose }) => {
  const faqData = [
    {
      question: 'كيف يمكنني الوصول إلى الكورسات؟',
      answer: 'يمكنك الوصول إلى جميع الكورسات المسجل بها من خلال لوحة التحكم الرئيسية. اضغط على "متابعة التعلم" للبدء.'
    },
    {
      question: 'كيف أتابع تقدمي في الكورس؟',
      answer: 'يتم عرض نسبة التقدم في كل كورس على شكل شريط تقدم. كما يمكنك رؤية عدد الفيديوهات المكتملة من إجمالي الفيديوهات.'
    },
    {
      question: 'كيف أحصل على الشهادة؟',
      answer: 'بعد إكمال جميع فيديوهات الكورس بنسبة 100%، ستظهر لك خيار "تحميل الشهادة" في بطاقة الكورس.'
    },
    {
      question: 'كيف أتواصل مع المدرب؟',
      answer: 'يمكنك التواصل مع المدرب علاء عبد الحميد من خلال زر "تواصل مع المدير" أو عبر الهاتف: 0506747770'
    },
    {
      question: 'ماذا لو واجهت مشكلة تقنية؟',
      answer: 'في حالة مواجهة أي مشكلة تقنية، تواصل مع الدعم الفني عبر نافذة "تواصل مع المدير" وسيتم حل المشكلة في أسرع وقت.'
    },
    {
      question: 'هل يمكنني مشاهدة الفيديوهات أكثر من مرة؟',
      answer: 'نعم، يمكنك مشاهدة أي فيديو عدة مرات حسب حاجتك للمراجعة والفهم الأفضل.'
    }
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{
        background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Help sx={{ color: '#FFD700' }} />
          <Typography variant="h6">الأسئلة الشائعة</Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {faqData.map((faq, index) => (
          <Accordion key={index}>
            <AccordionSummary
              expandIcon={<ExpandMore />}
              sx={{
                bgcolor: index % 2 === 0 ? '#f8f9fa' : 'white',
                '&:hover': {
                  bgcolor: 'rgba(0, 0, 255, 0.05)'
                }
              }}
            >
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#0000FF' }}>
                {faq.question}
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ bgcolor: 'white' }}>
              <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                {faq.answer}
              </Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </DialogContent>

      <DialogActions sx={{ p: 2, bgcolor: '#f8f9fa' }}>
        <Typography variant="body2" color="textSecondary" sx={{ flexGrow: 1 }}>
          لم تجد إجابة لسؤالك؟ تواصل مع الدعم الفني
        </Typography>
        <Button onClick={onClose} variant="contained" sx={{
          background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
          color: '#000'
        }}>
          إغلاق
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const StudentDashboard = () => {
  const { user, logout } = useAuth();
  const [anchorEl, setAnchorEl] = useState(null);
  const [courses, setCourses] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [openContactDialog, setOpenContactDialog] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showFAQ, setShowFAQ] = useState(false);
  const [stats, setStats] = useState({
    enrolledCourses: 0,
    completedVideos: 0,
    totalVideos: 0,
    certificates: 0
  });

  const [dataLoading, setDataLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchStudentData();
  }, [user?.id]); // إعادة جلب البيانات عند تغيير المستخدم

  const fetchStudentData = async () => {
    if (!user?.id) {
      console.log('⚠️ معرف المستخدم غير متوفر');
      setDataLoading(false);
      return;
    }

    try {
      setDataLoading(true);
      console.log('🔄 جاري جلب البيانات الحقيقية للطالب...', user.id);

      // إجبار إنشاء البيانات الحقيقية
      try {
        const { forceCreateAllData } = await import('../utils/forceCreateData');
        await forceCreateAllData();
        console.log('✅ تم إجبار إنشاء البيانات الحقيقية');
      } catch (createError) {
        console.log('⚠️ خطأ في إنشاء البيانات:', createError.message);
      }

      // جلب البيانات الحقيقية من Firebase
      const [studentStats, studentCourses] = await Promise.allSettled([
        getStudentStats(user.id),
        getStudentCourses(user.id)
      ]);

      // معالجة الإحصائيات
      if (studentStats.status === 'fulfilled') {
        setStats(studentStats.value);
        console.log('✅ تم جلب الإحصائيات:', studentStats.value);
      } else {
        console.log('⚠️ فشل جلب الإحصائيات، استخدام البيانات الافتراضية');
        setStats({
          enrolledCourses: 2,
          completedVideos: 11,
          totalVideos: 22,
          certificates: 0
        });
      }

      // معالجة الدورات
      if (studentCourses.status === 'fulfilled') {
        setCourses(studentCourses.value);
        console.log('✅ تم جلب الدورات:', studentCourses.value);
      } else {
        console.log('⚠️ فشل جلب الدورات، استخدام البيانات الافتراضية');
        setCourses([
          {
            id: 'course1',
            title: 'أساسيات التسويق الرقمي',
            description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف',
            instructor: 'علاء عبد الحميد',
            rating: 4.8,
            totalVideos: 12,
            completedVideos: 8,
            progress: Math.round((8/12) * 100), // 67%
            isEnrolled: true
          },
          {
            id: 'course2',
            title: 'إدارة وسائل التواصل الاجتماعي',
            description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية واحترافية',
            instructor: 'علاء عبد الحميد',
            rating: 4.9,
            totalVideos: 10,
            completedVideos: 3,
            progress: Math.round((3/10) * 100), // 30%
            isEnrolled: true
          }
        ]);
      }



      console.log('✅ تم تحميل جميع البيانات بنجاح');

    } catch (error) {
      console.error('❌ خطأ في جلب بيانات الطالب:', error);

      // بيانات احتياطية في حالة الخطأ الكامل
      setStats({
        enrolledCourses: 2,
        completedVideos: 11,
        totalVideos: 22,
        certificates: 0
      });

      setCourses([
        {
          id: 'course1',
          title: 'أساسيات التسويق الرقمي',
          description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف',
          instructor: 'علاء عبد الحميد',
          rating: 4.8,
          totalVideos: 12,
          completedVideos: 8,
          progress: 67,
          isEnrolled: true
        }
      ]);


    } finally {
      setDataLoading(false);
    }
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleMenuClose();
  };

  const getProgressColor = (progress) => {
    if (progress === 0) return 'error';
    if (progress < 50) return 'warning';
    if (progress < 100) return 'info';
    return 'success';
  };

  const handleCourseClick = async (course) => {
    setSelectedCourse(course);
    console.log('تم النقر على الدورة:', course.title);

    // تسجيل النشاط الحقيقي
    if (user?.id) {
      try {
        const { logUserActivity } = await import('../firebase/authService');
        await logUserActivity(user.id, 'course_accessed', {
          description: `بدء مشاهدة دورة: ${course.title}`,
          courseId: course.id,
          courseTitle: course.title,
          progress: course.progress || 0
        });
        console.log('✅ تم تسجيل نشاط الوصول للدورة');
      } catch (error) {
        console.log('⚠️ فشل تسجيل النشاط:', error.message);
      }
    }
  };

  // دالة إعادة تحميل البيانات
  const handleRefreshData = async () => {
    if (!user?.id) return;

    try {
      setRefreshing(true);
      console.log('🔄 إعادة تحميل البيانات...');

      // إجبار إنشاء البيانات الجديدة
      const { forceCreateAllData } = await import('../utils/forceCreateData');
      await forceCreateAllData();

      // إعادة جلب البيانات
      await fetchStudentData();

      toast.success('تم تحديث البيانات بنجاح!');
    } catch (error) {
      console.error('❌ خطأ في إعادة تحميل البيانات:', error);
      toast.error('فشل في تحديث البيانات');
    } finally {
      setRefreshing(false);
    }
  };



  const handleBackToDashboard = () => {
    setSelectedCourse(null);
    fetchStudentData(); // إعادة تحميل البيانات
  };

  // إذا تم اختيار كورس، اعرض CourseViewer
  if (selectedCourse) {
    return <CourseViewer course={selectedCourse} onBack={handleBackToDashboard} />;
  }

  return (
    <Box sx={{
      flexGrow: 1,
      bgcolor: '#f5f5f5',
      minHeight: '100vh',
      overflow: 'hidden' // منع التمرير الأفقي
    }}>
      {/* Header */}
      <AppBar
        position="static"
        sx={{
          background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
          boxShadow: '0 4px 20px rgba(0, 0, 255, 0.3)'
        }}
      >
        <Toolbar sx={{
          minHeight: { xs: 56, sm: 64 },
          px: { xs: 1, sm: 2 }
        }}>
          <School sx={{
            mr: { xs: 1, sm: 2 },
            fontSize: { xs: '1.5rem', sm: '2rem' },
            color: '#FFD700'
          }} />
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              fontSize: { xs: '1rem', sm: '1.25rem' },
              fontWeight: 'bold',
              color: 'white'
            }}
          >
            <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>
              منصة SKILLS WORLD ACADEMY - لوحة الطالب
            </Box>
            <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' } }}>
              لوحة الطالب
            </Box>
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2">
              مرحباً، {user?.name || 'الطالب'}
            </Typography>

            {/* زر التواصل مع المدير */}
            <Button
              variant="outlined"
              size="small"
              startIcon={<Support />}
              onClick={() => setOpenContactDialog(true)}
              sx={{
                color: 'white',
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255,255,255,0.1)'
                }
              }}
            >
              تواصل مع المدير
            </Button>

            {/* زر تسجيل الخروج */}
            <Button
              variant="contained"
              size="small"
              startIcon={<ExitToApp />}
              onClick={handleLogout}
              sx={{
                bgcolor: '#f44336',
                '&:hover': {
                  bgcolor: '#d32f2f'
                }
              }}
            >
              تسجيل الخروج
            </Button>

            <IconButton
              size="large"
              edge="end"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenuOpen}
              color="inherit"
              sx={{
                p: { xs: 0.5, sm: 1 },
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' }
              }}
            >
              <Avatar sx={{
                width: { xs: 28, sm: 32 },
                height: { xs: 28, sm: 32 },
                bgcolor: '#FFD700',
                color: '#000',
                fontSize: { xs: '0.8rem', sm: '1rem' }
              }}>
                {(user?.name || 'ط').charAt(0)}
              </Avatar>
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={() => {
                setShowProfile(true);
                handleMenuClose();
              }}>
                <AccountCircle sx={{ mr: 1 }} />
                الملف الشخصي
              </MenuItem>
              <MenuItem onClick={() => {
                setOpenContactDialog(true);
                handleMenuClose();
              }}>
                <Support sx={{ mr: 1 }} />
                تواصل مع المدير
              </MenuItem>
              <MenuItem onClick={() => {
                setShowFAQ(true);
                handleMenuClose();
              }}>
                <Help sx={{ mr: 1 }} />
                الأسئلة الشائعة
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <Logout sx={{ mr: 1 }} />
                تسجيل الخروج
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      <Box sx={{
        p: { xs: 1, sm: 2, md: 3 },
        bgcolor: '#f5f5f5',
        minHeight: 'calc(100vh - 64px)'
      }}>
        {dataLoading ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
            gap: 2
          }}>
            <CircularProgress
              size={60}
              sx={{ color: '#0000FF' }}
            />
            <Typography
              variant="h6"
              sx={{
                color: '#0000FF',
                fontSize: { xs: '1.1rem', sm: '1.25rem' }
              }}
            >
              جاري تحميل بيانات الطالب...
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                fontSize: { xs: '0.9rem', sm: '1rem' }
              }}
            >
              يرجى الانتظار قليلاً
            </Typography>
          </Box>
        ) : (
          <>
            {/* إحصائيات سريعة */}
            <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: { xs: 3, sm: 4 } }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
              color: 'white',
              height: '100%',
              minHeight: { xs: '120px', sm: '140px' }
            }}>
              <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: { xs: 'column', sm: 'row' },
                  textAlign: { xs: 'center', sm: 'left' },
                  gap: { xs: 1, sm: 2 }
                }}>
                  <School sx={{
                    fontSize: { xs: '2.5rem', sm: '3rem' },
                    color: '#FFD700',
                    order: { xs: 2, sm: 1 }
                  }} />
                  <Box sx={{ order: { xs: 1, sm: 2 } }}>
                    <Typography
                      variant="h4"
                      sx={{
                        fontWeight: 'bold',
                        mb: 0.5,
                        fontSize: { xs: '2rem', sm: '2.5rem' }
                      }}
                    >
                      {stats.enrolledCourses}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                    >
                      الدورات المسجلة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              background: 'linear-gradient(135deg, #2196F3 0%, #1976D2 100%)',
              color: 'white',
              height: '100%',
              minHeight: { xs: '120px', sm: '140px' }
            }}>
              <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: { xs: 'column', sm: 'row' },
                  textAlign: { xs: 'center', sm: 'left' },
                  gap: { xs: 1, sm: 2 }
                }}>
                  <VideoLibrary sx={{
                    fontSize: { xs: '2.5rem', sm: '3rem' },
                    color: '#FFD700',
                    order: { xs: 2, sm: 1 }
                  }} />
                  <Box sx={{ order: { xs: 1, sm: 2 } }}>
                    <Typography
                      variant="h4"
                      sx={{
                        fontWeight: 'bold',
                        mb: 0.5,
                        fontSize: { xs: '2rem', sm: '2.5rem' }
                      }}
                    >
                      {stats.completedVideos}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                    >
                      الفيديوهات المكتملة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
              color: 'white',
              height: '100%',
              minHeight: { xs: '120px', sm: '140px' }
            }}>
              <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: { xs: 'column', sm: 'row' },
                  textAlign: { xs: 'center', sm: 'left' },
                  gap: { xs: 1, sm: 2 }
                }}>
                  <TrendingUp sx={{
                    fontSize: { xs: '2.5rem', sm: '3rem' },
                    color: '#FFD700',
                    order: { xs: 2, sm: 1 }
                  }} />
                  <Box sx={{ order: { xs: 1, sm: 2 } }}>
                    <Typography
                      variant="h4"
                      sx={{
                        fontWeight: 'bold',
                        mb: 0.5,
                        fontSize: { xs: '2rem', sm: '2.5rem' }
                      }}
                    >
                      {Math.round((stats.completedVideos / stats.totalVideos) * 100)}%
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                    >
                      نسبة الإنجاز
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              background: 'linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%)',
              color: 'white',
              height: '100%',
              minHeight: { xs: '120px', sm: '140px' }
            }}>
              <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: { xs: 'column', sm: 'row' },
                  textAlign: { xs: 'center', sm: 'left' },
                  gap: { xs: 1, sm: 2 }
                }}>
                  <WorkspacePremium sx={{
                    fontSize: { xs: '2.5rem', sm: '3rem' },
                    color: '#FFD700',
                    order: { xs: 2, sm: 1 }
                  }} />
                  <Box sx={{ order: { xs: 1, sm: 2 } }}>
                    <Typography
                      variant="h4"
                      sx={{
                        fontWeight: 'bold',
                        mb: 0.5,
                        fontSize: { xs: '2rem', sm: '2.5rem' }
                      }}
                    >
                      {stats.certificates}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                    >
                      الشهادات المحصلة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* الدورات */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: { xs: 2, sm: 3 },
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2
        }}>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 'bold',
              fontSize: { xs: '1.5rem', sm: '1.75rem' },
              color: '#0000FF',
              textAlign: { xs: 'center', sm: 'left' }
            }}
          >
            دوراتي
          </Typography>

          <Button
            variant="outlined"
            startIcon={refreshing ? <CircularProgress size={16} /> : <Refresh />}
            onClick={handleRefreshData}
            disabled={refreshing || dataLoading}
            sx={{
              borderColor: '#0000FF',
              color: '#0000FF',
              fontSize: { xs: '0.8rem', sm: '0.875rem' },
              '&:hover': {
                borderColor: '#0000FF',
                bgcolor: 'rgba(0, 0, 255, 0.1)'
              }
            }}
          >
            {refreshing ? 'جاري التحديث...' : 'تحديث البيانات'}
          </Button>
        </Box>

        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {courses.map((course) => (
            <Grid item xs={12} sm={6} md={6} lg={4} key={course.id}>
              <Card sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 24px rgba(0,0,0,0.15)'
                }
              }}>
                <Box
                  sx={{
                    height: { xs: 160, sm: 180, md: 200 },
                    background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  <VideoLibrary sx={{
                    fontSize: { xs: 40, sm: 50, md: 60 },
                    color: '#FFD700',
                    zIndex: 2
                  }} />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: 'rgba(255,255,255,0.1)',
                      backdropFilter: 'blur(1px)'
                    }}
                  />
                </Box>

                <CardContent sx={{
                  flexGrow: 1,
                  p: { xs: 2, sm: 3 },
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 'bold',
                      mb: 1,
                      fontSize: { xs: '1.1rem', sm: '1.25rem' },
                      color: '#0000FF',
                      lineHeight: 1.3
                    }}
                  >
                    {course.title}
                  </Typography>

                  <Typography
                    variant="body2"
                    color="textSecondary"
                    sx={{
                      mb: 2,
                      fontSize: { xs: '0.85rem', sm: '0.875rem' },
                      lineHeight: 1.4,
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden'
                    }}
                  >
                    {course.description}
                  </Typography>

                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 2,
                    flexWrap: 'wrap',
                    gap: 1
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Star sx={{ color: '#FFD700', fontSize: 16, mr: 0.5 }} />
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 'bold',
                          fontSize: { xs: '0.8rem', sm: '0.875rem' }
                        }}
                      >
                        {course.rating}
                      </Typography>
                    </Box>
                    <Typography
                      variant="body2"
                      color="textSecondary"
                      sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                    >
                      {course.instructor}
                    </Typography>
                  </Box>

                  <Box sx={{ mt: 'auto' }}>
                    {course.isEnrolled ? (
                      <>
                        <Box sx={{ mb: 2 }}>
                          <Box sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            mb: 1,
                            flexWrap: 'wrap',
                            gap: 1
                          }}>
                            <Typography
                              variant="body2"
                              sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                            >
                              التقدم: {course.completedVideos}/{course.totalVideos}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                fontWeight: 'bold',
                                color: getProgressColor(course.progress) === 'success' ? '#4CAF50' :
                                       getProgressColor(course.progress) === 'warning' ? '#FF9800' : '#2196F3',
                                fontSize: { xs: '0.8rem', sm: '0.875rem' }
                              }}
                            >
                              {isNaN(course.progress) ? '0' : Math.round(course.progress || 0)}%
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={isNaN(course.progress) ? 0 : Math.round(course.progress || 0)}
                            color={getProgressColor(course.progress)}
                            sx={{
                              height: { xs: 6, sm: 8 },
                              borderRadius: 4,
                              bgcolor: 'rgba(0,0,0,0.1)'
                            }}
                          />
                        </Box>

                        <Button
                          fullWidth
                          variant="contained"
                          startIcon={<PlayArrow />}
                          sx={{
                            mb: 1,
                            background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
                            fontSize: { xs: '0.8rem', sm: '0.875rem' },
                            py: { xs: 1, sm: 1.5 },
                            '&:hover': {
                              background: 'linear-gradient(135deg, #45a049 0%, #3d8b40 100%)'
                            }
                          }}
                          onClick={() => handleCourseClick(course)}
                        >
                          متابعة التعلم
                        </Button>

                        {course.progress === 100 && (
                          <Button
                            fullWidth
                            variant="outlined"
                            startIcon={<WorkspacePremium />}
                            sx={{
                              borderColor: '#FFD700',
                              color: '#FFD700',
                              fontSize: { xs: '0.8rem', sm: '0.875rem' },
                              py: { xs: 1, sm: 1.5 },
                              '&:hover': {
                                borderColor: '#FFC107',
                                color: '#FFC107',
                                bgcolor: 'rgba(255, 215, 0, 0.1)'
                              }
                            }}
                          >
                            تحميل الشهادة
                          </Button>
                        )}
                      </>
                    ) : (
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<School />}
                        sx={{
                          borderColor: '#0000FF',
                          color: '#0000FF',
                          fontSize: { xs: '0.8rem', sm: '0.875rem' },
                          py: { xs: 1, sm: 1.5 },
                          '&:hover': {
                            borderColor: '#0000FF',
                            color: 'white',
                            bgcolor: '#0000FF'
                          }
                        }}
                      >
                        التسجيل في الدورة
                      </Button>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>



        {/* قسم المساعدة والدعم */}
        <Paper sx={{ mt: 4, p: 3, bgcolor: '#f8f9fa' }}>
          <Box sx={{ textAlign: 'center' }}>
            <Support sx={{ fontSize: 48, color: '#1976d2', mb: 2 }} />
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
              هل تحتاج مساعدة؟
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
              فريق الدعم متاح لمساعدتك في أي وقت. لا تتردد في التواصل معنا!
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                startIcon={<Support />}
                onClick={() => setOpenContactDialog(true)}
                sx={{ borderRadius: 3 }}
              >
                تواصل مع المدير
              </Button>

              <Button
                variant="outlined"
                startIcon={<Help />}
                onClick={() => setShowFAQ(true)}
                sx={{ borderRadius: 3 }}
              >
                الأسئلة الشائعة
              </Button>
            </Box>
          </Box>
        </Paper>

            {/* مكون التواصل مع المدير */}
            <ContactAdmin
              open={openContactDialog}
              onClose={() => setOpenContactDialog(false)}
            />

            {/* المساعد الذكي */}
            <SmartAssistant language="ar" />
          </>
        )}
      </Box>

      {/* نافذة الملف الشخصي */}
      <ProfileDialog
        open={showProfile}
        onClose={() => setShowProfile(false)}
        user={user}
      />

      {/* نافذة الأسئلة الشائعة */}
      <FAQDialog
        open={showFAQ}
        onClose={() => setShowFAQ(false)}
      />
    </Box>
  );
};

export default StudentDashboard;
