import { 
  collection, 
  doc, 
  setDoc, 
  addDoc,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../firebase/config';

// إنشاء بيانات تجريبية في Firebase

/**
 * إنشاء مستخدمين تجريبيين
 */
export const createTestUsers = async () => {
  try {
    console.log('🔧 إنشاء مستخدمين تجريبيين...');

    // طالب 1
    await setDoc(doc(db, 'users', 'student1'), {
      id: 'student1',
      name: 'أحمد محمد علي',
      email: '<EMAIL>',
      phone: '0501234567',
      bio: 'طالب مجتهد في منصة SKILLS WORLD ACADEMY',
      studentCode: '123456',
      role: 'student',
      enrolledCourses: ['course1', 'course2'],
      completedCourses: [],
      certificates: [],
      totalWatchTime: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLoginAt: serverTimestamp(),
      isActive: true
    });

    // طالب 2
    await setDoc(doc(db, 'users', 'student2'), {
      id: 'student2',
      name: 'فاطمة أحمد',
      email: '<EMAIL>',
      phone: '0507654321',
      bio: 'طالبة متميزة تسعى للتطوير المهني',
      studentCode: '789012',
      role: 'student',
      enrolledCourses: ['course1'],
      completedCourses: [],
      certificates: [],
      totalWatchTime: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLoginAt: serverTimestamp(),
      isActive: true
    });

    // مدير
    await setDoc(doc(db, 'users', 'admin1'), {
      id: 'admin1',
      name: 'علاء عبد الحميد',
      email: '<EMAIL>',
      phone: '0506747770',
      bio: 'مدرب ومؤسس منصة SKILLS WORLD ACADEMY',
      role: 'admin',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLoginAt: serverTimestamp(),
      isActive: true
    });

    console.log('✅ تم إنشاء المستخدمين التجريبيين بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدمين التجريبيين:', error);
    throw error;
  }
};

/**
 * إنشاء كورسات تجريبية
 */
export const createTestCourses = async () => {
  try {
    console.log('🔧 إنشاء كورسات تجريبية...');

    // كورس 1
    await setDoc(doc(db, 'courses', 'course1'), {
      id: 'course1',
      title: 'أساسيات التسويق الرقمي',
      description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف',
      instructor: 'علاء عبد الحميد',
      category: 'تسويق',
      level: 'مبتدئ',
      duration: '8 ساعات',
      totalVideos: 12,
      rating: 4.8,
      studentsCount: 150,
      price: 299,
      thumbnail: 'https://via.placeholder.com/300x200?text=Digital+Marketing',
      isPublished: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // كورس 2
    await setDoc(doc(db, 'courses', 'course2'), {
      id: 'course2',
      title: 'إدارة وسائل التواصل الاجتماعي',
      description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية واحترافية',
      instructor: 'علاء عبد الحميد',
      category: 'تسويق',
      level: 'متوسط',
      duration: '6 ساعات',
      totalVideos: 10,
      rating: 4.9,
      studentsCount: 120,
      price: 399,
      thumbnail: 'https://via.placeholder.com/300x200?text=Social+Media',
      isPublished: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log('✅ تم إنشاء الكورسات التجريبية بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء الكورسات التجريبية:', error);
    throw error;
  }
};

/**
 * إنشاء تقدم تجريبي للطلاب
 */
export const createTestProgress = async () => {
  try {
    console.log('🔧 إنشاء تقدم تجريبي للطلاب...');

    // تقدم الطالب 1 في الكورس 1
    await setDoc(doc(db, 'userProgress', 'student1_course1'), {
      userId: 'student1',
      courseId: 'course1',
      completedVideos: 8,
      totalVideos: 12,
      progress: 67,
      lastWatchedVideo: 'video8',
      totalWatchTime: 240, // بالدقائق
      isCompleted: false,
      startedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // تقدم الطالب 1 في الكورس 2
    await setDoc(doc(db, 'userProgress', 'student1_course2'), {
      userId: 'student1',
      courseId: 'course2',
      completedVideos: 3,
      totalVideos: 10,
      progress: 30,
      lastWatchedVideo: 'video3',
      totalWatchTime: 90,
      isCompleted: false,
      startedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // تقدم الطالب 2 في الكورس 1
    await setDoc(doc(db, 'userProgress', 'student2_course1'), {
      userId: 'student2',
      courseId: 'course1',
      completedVideos: 5,
      totalVideos: 12,
      progress: 42,
      lastWatchedVideo: 'video5',
      totalWatchTime: 150,
      isCompleted: false,
      startedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log('✅ تم إنشاء التقدم التجريبي بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء التقدم التجريبي:', error);
    throw error;
  }
};

/**
 * إنشاء إعدادات عامة
 */
export const createTestSettings = async () => {
  try {
    console.log('🔧 إنشاء إعدادات عامة...');

    await setDoc(doc(db, 'settings', 'general'), {
      platformName: 'SKILLS WORLD ACADEMY',
      instructorName: 'علاء عبد الحميد',
      contactEmail: '<EMAIL>',
      contactPhone: '0506747770',
      supportEnabled: true,
      registrationEnabled: true,
      maintenanceMode: false,
      version: '1.0.0',
      updatedAt: serverTimestamp()
    });

    console.log('✅ تم إنشاء الإعدادات العامة بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء الإعدادات العامة:', error);
    throw error;
  }
};

/**
 * تشغيل جميع عمليات إنشاء البيانات التجريبية
 */
export const seedFirebaseData = async () => {
  try {
    console.log('🚀 بدء إنشاء البيانات التجريبية في Firebase...');

    await createTestUsers();
    await createTestCourses();
    await createTestProgress();
    await createTestSettings();

    console.log('🎉 تم إنشاء جميع البيانات التجريبية بنجاح!');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error);
    throw error;
  }
};

/**
 * اختبار الاتصال بـ Firebase
 */
export const testFirebaseConnection = async () => {
  try {
    console.log('🧪 اختبار الاتصال بـ Firebase...');
    
    // محاولة إنشاء مستند تجريبي
    const testDoc = await addDoc(collection(db, 'test'), {
      message: 'اختبار الاتصال',
      timestamp: serverTimestamp()
    });

    console.log('✅ تم الاتصال بـ Firebase بنجاح! معرف المستند:', testDoc.id);
    return true;
  } catch (error) {
    console.error('❌ فشل الاتصال بـ Firebase:', error);
    throw error;
  }
};

export default {
  createTestUsers,
  createTestCourses,
  createTestProgress,
  createTestSettings,
  seedFirebaseData,
  testFirebaseConnection
};
